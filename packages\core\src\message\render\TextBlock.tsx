import mermaid from "mermaid";
import React, { useMemo } from "react";
import Markdown, { MarkdownHooks } from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeMermaid from "rehype-mermaid";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

mermaid.initialize({ startOnLoad: false });

interface TextBlockProps {
  content: string;
}

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;

  // 优化markdown闪烁问题
  const markdown = useMemo(() => {
    return (
      <Markdown rehypePlugins={[rehypeHighlight, rehypeRaw]} remarkPlugins={[remarkGfm]} components={{
        code({ node, inline, className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || "");
          return !inline && match ? (
            <div className="ag:overflow-auto">
              <pre
                className={className}
                {...props}
              >
                <code>{children}</code>
              </pre>
            </div>
          ) : (
            <code className={className} {...props}>
              {children}

      }}>
        {content}
      </Markdown>
    );
  }, [content]);

  return <div className="cscs-agent-text-block ag:break-all ag:leading-[1.6em]">{markdown}</div>;
};

export default TextBlock;
