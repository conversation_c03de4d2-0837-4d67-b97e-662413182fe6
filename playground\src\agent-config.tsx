import { dynamicPageConfigFactory } from "@cscs-agent/agents";
import type { AgentChatConfig } from "@cscs-agent/core";

import InsertText from "./widgets/insert-text";

const PreviewWidget = () => {
  return <div>PreviewWidget</div>;
};

const dynamicPageAgentConfig = dynamicPageConfigFactory({
  saveApiUrl: () => {
    const host = location.host;
    const protocol = location.protocol;
    return `${protocol}//${host}/cluster-api/page/system/dynamic/page/manage/savePageForAi`;
  },
  previewUrl: (id: string) => `http://************:8002/dynamic-page/temporary-preview?id=${id}`,
});

export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    {
      name: "问答助手",
      code: "dynamic-page-qa",
      logo: "/assets/dynamic-page-qa-logo.png",
      welcome: "Hi，欢迎使用问答助手",
      description: "面向动态页面管理场景，提供自然语言交互式解答，提升配置效率",
      message: {
        blocks: {
          widgets: [],
        },
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      prompts: [
        {
          title: "只是一个提示词",
          content: "请查询{{[企业名称]}}获取企业信息",
        },
      ],
      commands: [
        {
          name: "",
          action: (params) => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "PreviewWidget",
              component: PreviewWidget,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "get",
        },
      },
    },
    {
      name: "测试智能体",
      code: "default",
      message: {
        blocks: {
          widgets: [],
        },
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      prompts: [
        {
          title: "只是一个提示词",
          content: "请查询{{[企业名称]}}获取企业信息",
        },
      ],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          header: {
            widgets: [
              {
                code: "InsertText",
                component: InsertText,
              },
            ],
          },
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "PreviewWidget",
              component: PreviewWidget,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/test",
          headers: {},
          method: "get",
        },
      },
    },
  ],
};
