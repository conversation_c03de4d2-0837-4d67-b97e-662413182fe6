import { Tooltip } from "antd";
import React from "react";

import { Node, mergeAttributes } from "@tiptap/core";
import { NodeViewWrapper } from "@tiptap/react";
import { ReactNodeViewRenderer } from "@tiptap/react";

interface TagProps {
  name: string;
  rawValue: string;
  tooltips?: string;
}

const Tag: React.FC<TagProps> = (props) => {
  const { name, tooltips } = props;

  return (
    <NodeViewWrapper>
      <Tooltip title={tooltips}>
        <span className="ag:px-2 ag:py-1 ag:rounded-md ag:bg-[rgba(0,0,0,0.05)] ag:text-[rgba(0,0,0,0.65)]">
          {name}
        </span>
      </Tooltip>
    </NodeViewWrapper>
  );
};


export default Node.create({
  name: "reactComponent",

  group: "block",

  atom: true,

  addAttributes() {
    return {
      count: {
        default: 0,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "embedded-tag",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["react-component", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(Tag);
  },
});
