{"name": "@cscs-agent/core", "version": "0.1.2", "description": "", "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"dev": "vite build --mode development --watch --minify false", "build": "vite build", "lint": "eslint ./src --ext .ts,.tsx --fix", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@ant-design/x": "^1.4.0", "@tiptap/core": "^2.13.0", "@tiptap/pm": "^2.13.0", "@tiptap/react": "^2.13.0", "@tiptap/starter-kit": "^2.13.0", "ahooks": "^3.8.5", "axios": "^1.6.7", "eventemitter3": "^5.0.1", "fast-xml-parser": "^5.2.3", "jotai": "^2.12.3", "mermaid": "^11.6.0", "nanoid": "^5.1.5", "penpal": "^7.0.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-mermaid": "^3.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@ant-design/icons": "^5.6.1", "@cscs-agent/icons": "workspace:^0.1.0", "@eslint/js": "^9.25.1", "@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/vite": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "22.15.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "antd": "^5.24.3", "antd-style": "^3.7.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "jsdom": "^26.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.5.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.2", "vite": "^6.2.0", "vite-plugin-dts": "^4.5.3", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.3"}, "peerDependencies": {"@ant-design/icons": "^5.6.1", "antd": "^5.24.3", "antd-style": "^3.7.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.5.2"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0"}